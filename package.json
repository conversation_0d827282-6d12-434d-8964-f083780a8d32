{"name": "image-compression-nodejs", "type": "module", "scripts": {"dev": "tsx watch ./src/app.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@fastify/multipart": "^9.0.3", "@hono/node-server": "^1.14.1", "@hono/zod-validator": "^0.7.0", "@libsql/client": "^0.15.9", "@paralleldrive/cuid2": "^2.2.2", "bullmq": "^5.56.4", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "es-toolkit": "^1.39.4", "fastify": "^5.4.0", "fastify-type-provider-zod": "^5.0.2", "file-type": "^21.0.0", "hono": "^4.7.8", "hyperid": "^3.3.0", "ioredis": "^5.6.1", "nanoid": "^5.1.5", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.7", "pretty-bytes": "^7.0.0", "sharp": "^0.34.1", "type-fest": "^4.41.0", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^20.11.17", "drizzle-kit": "^0.31.1", "tsx": "^4.7.1", "typescript": "^5.8.3"}}