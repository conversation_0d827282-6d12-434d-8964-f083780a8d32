#!/bin/bash

# Script for rootless <PERSON><PERSON> with additional permission handling
set -e

# Check if podman-compose is installed
if ! command -v podman-compose &> /dev/null; then
    echo "podman-compose is not installed. Please install it first."
    exit 1
fi

# Get current user ID and group ID
USER_ID=$(id -u)
GROUP_ID=$(id -g)

# Export for docker-compose to use
export USER_ID
export GROUP_ID

echo "Starting development environment for rootless <PERSON>dman..."
echo "Using USER_ID=$USER_ID and GROUP_ID=$GROUP_ID"

# Ensure the static directory exists and has correct permissions
mkdir -p static/compress_images
chmod 755 static/compress_images

# For rootless Podman, we might need to set SELinux context
if command -v getenforce &> /dev/null && [[ $(getenforce) != "Disabled" ]]; then
    echo "Setting SELinux context for volume mounts..."
    chcon -Rt container_file_t . 2>/dev/null || true
fi

# Start development environment with hot reload
podman-compose -f docker-compose.dev.yaml up --build

# Cleanup
echo "Stopping development environment..."
podman-compose -f docker-compose.dev.yaml down
