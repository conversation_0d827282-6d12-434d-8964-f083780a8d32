# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# Security
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long

# Database
DATABASE_URL=postgres://postgres:postgres@localhost:5432/compress_images

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Static Files
STATIC_PATH=/static

# Image Processing
ADJUST_IMAGE_DEFAULT_QUALITY=80
ADJUST_IMAGE_MAX_FILE_SIZE=52428800  # 50MB in bytes
ADJUST_IMAGE_MAX_FILES=10
