#!/bin/bash

# Ensure script stops on first error
set -e

# Check if podman-compose is installed
if ! command -v podman-compose &> /dev/null; then
    echo "podman-compose is not installed. Please install it first."
    exit 1
fi

# Get current user ID and group ID
USER_ID=$(id -u)
GROUP_ID=$(id -g)

# Export for docker-compose to use
export USER_ID
export GROUP_ID

echo "Starting development environment with hot reload..."
echo "Using USER_ID=$USER_ID and GROUP_ID=$GROUP_ID"

# Start development environment with hot reload
podman-compose -f docker-compose.dev.yaml up --build

# This script will be interrupted by Ctrl+C, which will trigger podman-compose down
# The following will only execute if podman-compose up exits normally
echo "Stopping development environment..."
podman-compose -f docker-compose.dev.yaml down
