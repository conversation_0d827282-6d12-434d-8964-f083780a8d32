version: "3.8"
services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=supersecret
      - DB_URL=************************************/compress_images
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - db
      - redis
  redis:
    image: redis:7.0.11
    ports:
      - "6379:6379"
  db:
    image: postgres:15.3
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=compress_images
    volumes:
      - db_data:/var/lib/postgresql/data
volumes:
  db_data:
