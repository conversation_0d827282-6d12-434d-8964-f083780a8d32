import type stream from "node:stream";
import type { Logger } from "pino";
import { createFriendlyFileName, getFileExtension } from "../utils/file";
import logger from "../utils/logger";
import type { FileService } from "./file.service";
import { addCompressImageJob } from "../queues";

export class ImageService {
  private fileService: FileService;
  private logger: Logger;

  constructor(fileService: FileService) {
    this.fileService = fileService;
    this.logger = logger;
  }

  async upload(args: {
    file: stream.Readable & { truncated: boolean };
    fileName: string;
    mimeType: string;
    onTruncated?: () => void;
  }): Promise<string> {
    const { file, fileName, mimeType, onTruncated } = args;

    try {
      let { truncated: isTruncated } = file;

      const storagePath = await this.fileService.uploadStream({
        fileName,
        mimeType,
        readStream: file,
        onFinish: () => {
          isTruncated = file.truncated;
        },
        onError: (error) => {
          this.logger.error(error);

          throw error;
        },
      });

      if (isTruncated) {
        onTruncated?.();
      }

      return storagePath;
    } catch (error) {
      throw new Error("Upload File Fail!");
    }
  }

  async uploadAndCompress(args: {
    file: stream.Readable & { truncated: boolean };
    fileName: string;
    mimeType: string;
    quality: number;
    targetSize: number;
    onTruncated?: () => void;
  }): Promise<void> {
    const { file, mimeType, quality, onTruncated } = args;

    const fileName = createFriendlyFileName(args.fileName);

    try {
      const storagePath = await this.upload({
        file,
        fileName,
        mimeType,
        onTruncated,
      });

      const fileExt = getFileExtension(fileName);

      if (!fileExt) {
        throw new Error(`Can not parse file extension, file name: ${fileName}`);
      }

      addCompressImageJob({
        storagePath,
        options: {
          quality,
          targetFormat: fileExt,
        },
      });

      //   const imageBuffer = await file.arrayBuffer();

      //   const compressSize = await compressImageToTargetSize({
      //     buffer: imageBuffer,
      //     outputStream: fileWriteStream,
      //     targetSizeInBytes: targetSize,
      //     imageOptions: {
      //       quality,
      //       targetFormat: fileExt,
      //     },
      //     logger: this.logger.child({
      //       fileName,
      //     }),
      //   });

      //   return compressSize;
    } catch (error) {
      throw new Error("Compress File Fail!");
    }
  }
}
