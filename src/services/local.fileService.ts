import fs from "node:fs";
import type { Readable, Writable } from "node:stream";
import type { FileService } from "./file.service";
import type { FileRepository } from "../repositories/file.repository";
import { appConfig } from "../config";
import { STORAGE_TYPE } from "../constants/file";

export class LocalFileService implements FileService {
  folderPath: string;
  fileRepository: FileRepository;

  constructor(folderPath: string, fileRepository: FileRepository) {
    this.folderPath = `${appConfig.staticPath}/${folderPath}`;
    this.fileRepository = fileRepository;

    if (!fs.existsSync(this.folderPath)) {
      fs.mkdirSync(this.folderPath, { recursive: true });
    }
  }

  async delete(fileName: string): Promise<void> {
    const filePath = this.getFilePath(fileName);

    try {
      await fs.promises.unlink(filePath);
    } catch (error) {
      throw new Error(`Failed to delete file, path ${filePath}`);
    }
  }

  async getSize(fileName: string): Promise<number> {
    const filePath = this.getFilePath(fileName);

    try {
      const stats = await fs.promises.stat(filePath);
      return stats.size;
    } catch (error) {
      throw new Error(`Failed to get file size, path ${filePath}`);
    }
  }

  async write(fileName: string, data: Buffer) {
    const filePath = this.getFilePath(fileName);

    try {
      await fs.promises.writeFile(filePath, data);
    } catch (error) {
      throw new Error(`Failed to write file, path ${filePath}`);
    }
  }

  createWriteStream(fileName: string): Writable {
    const filePath = this.getFilePath(fileName);

    return fs.createWriteStream(filePath);
  }

  createReadStream(fileName: string): Readable {
    const filePath = this.getFilePath(fileName);

    return fs.createReadStream(filePath);
  }

  getFilePath(fileName: string) {
    return `${this.folderPath}/${fileName}`;
  }

  async uploadStream(args: {
    fileName: string;
    mimeType: string;
    readStream: Readable;
    options?: {
      onError?: (error: Error) => void;
      onFinish?: () => void;
    };
  }): Promise<string> {
    const { fileName, mimeType, readStream, options } = args;

    const storagePath = this.getFilePath(fileName);

    try {
      const writeStream = this.createWriteStream(fileName);

      await new Promise((resolve, reject) => {
        readStream.pipe(writeStream);

        readStream.on("error", async (error) => {
          await this.delete(fileName);
          options?.onError?.(error);
          reject(error);
        });

        writeStream.on("error", async (error) => {
          await this.delete(fileName);
          options?.onError?.(error);
          reject(error);
        });

        writeStream.on("finish", () => {
          options?.onFinish?.();
          resolve(undefined);
        });
      });

      await this.fileRepository.create({
        name: fileName,
        mimeType,
        storagePath: storagePath,
        uploadTimestamp: new Date(),
        storageType: STORAGE_TYPE.LOCAL,
      });

      return storagePath;
    } catch (error) {
      await this.delete(fileName);

      throw new Error(`Failed to write file, path ${storagePath}`);
    }
  }
}
