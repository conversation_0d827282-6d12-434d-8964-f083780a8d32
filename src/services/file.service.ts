import type Stream from "node:stream";

export abstract class FileService {
  abstract getSize(fileName: string): Promise<number>;
  abstract delete(fileName: string): Promise<void>;
  abstract write(fileName: string, data: Buffer): Promise<void>;
  abstract createWriteStream(fileName: string): Stream.Writable;
  abstract createReadStream(fileName: string): Stream.Readable;
  abstract uploadStream(params: {
    fileName: string;
    readStream: Stream.Readable;
    mimeType: string;
    onError?: (error: Error) => void;
    onFinish?: () => void;
  }): Promise<string>;
}
