import {
  Transform,
  type TransformCallback,
  type TransformOptions,
} from "node:stream";

export class ByteCounterTransform extends Transform {
  totalBytes: number;

  constructor(options?: TransformOptions) {
    super(options);
    this.totalBytes = 0;
  }

  _transform(chunk: Buffer, _encoding: string, callback: TransformCallback) {
    this.totalBytes += chunk.length;
    this.push(chunk); // Quan trọng: Đẩy chunk đi để các stream tiếp theo có thể xử lý
    callback();
  }

  // Khi stream kết thúc, bạn có thể lấy tổng số bytes
  _flush(callback: TransformCallback) {
    // <PERSON><PERSON> thể emit một sự kiện hoặc gọi một callback ở đây nếu cần báo cáo ngay
    // console.log(`Final counted bytes: ${this.totalBytes}`);
    callback();
  }
}
