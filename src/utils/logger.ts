import pino from "pino";
import { appConfig } from "../config";

const logger = pino({
  level: appConfig.logLevel,
  transport: {
    target: "pino-pretty",
    options: {
      colorize: true,
      translateTime: "SYS:standard",
      ignore: "pid,hostname",
    },
  },
  base: {
    env: appConfig.env,
  },
});

if (appConfig.env === "production") {
  logger.child({ environment: "production" });
}

export default logger;
