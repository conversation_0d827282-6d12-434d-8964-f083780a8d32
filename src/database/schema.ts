import { pgTable, serial, varchar, text, timestamp } from "drizzle-orm/pg-core";

export const filesTable = pgTable("images", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  mimeType: varchar("mime_type", { length: 100 }).notNull(),
  storageType: varchar("storage_type", { length: 50 }).notNull(),
  storagePath: text("storage_path").notNull(),
  uploadTimestamp: timestamp("upload_timestamp").notNull(),
});

export type File = typeof filesTable.$inferSelect;
export type InsertFile = typeof filesTable.$inferInsert;
