import { ZodError } from "zod/v4";
import type { FastifyError, FastifyReply, FastifyRequest } from "fastify";
import { appConfig } from "../config";
import logger from "../utils/logger";
import { ApiError, ValidationError } from "../types/errors";

export const errorHandler = async (
  err: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply,
) => {
  let statusCode = 500;
  let message = "Internal Server Error";
  let code = "INTERNAL_SERVER_ERROR";
  let details: unknown = undefined;

  if (appConfig.env === "development" || appConfig.env === "staging") {
    logger.error(
      { err, url: request.url, stack: err.stack },
      "An unhandled error occurred",
    );
  } else {
    logger.error(
      { err: err.message, url: request.url },
      "An unhandled error occurred",
    );
  }

  if (err instanceof ZodError) {
    const validationError = new ValidationError(err);

    statusCode = validationError.statusCode;
    message = validationError.message;
    details = validationError.details;
    code = validationError.code || code;
  } else if (err instanceof ApiError) {
    statusCode = err.statusCode;
    message = err.message;
    code = err.code || code;
    details = err.details;
  } else if (err instanceof Error) {
    message = err.message;
  }

  const errorResponse = {
    statusCode: statusCode,
    message: message,
    code: code,
    timestamp: new Date().toISOString(),
    path: request.url,
    details,
    ...(appConfig.env === "development" && { stack: err.stack }),
  };

  return reply.status(statusCode).send(errorResponse);
};
