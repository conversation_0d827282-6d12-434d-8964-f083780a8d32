import "dotenv/config";
import { z } from "zod/v4";
import { Redis } from "ioredis";

const configSchema = z
  .object({
    port: z.coerce.number().int().min(1024).max(65535).default(3000),
    jwtSecret: z.string().min(32),
    staticPath: z.string().default("/static"),
    dbUrl: z.string(),
    env: z
      .enum(["development", "production", "test", "staging"])
      .default("development"),
    logLevel: z
      .enum(["fatal", "error", "warn", "info", "debug", "trace", "silent"])
      .default("debug"),
    adjustImage: z.object({
      defaultQuality: z.coerce.number().min(1).max(100).default(80),
      maxFileSize: z.coerce
        .number()
        .int()
        .default(50 * 1024 ** 2),
      maxFiles: z.coerce.number().int().default(10),
    }),
  })
  .check((ctx) => {
    const { value } = ctx;

    const { env, logLevel } = value;

    if (
      env === "production" &&
      (logLevel === "debug" || logLevel === "trace")
    ) {
      ctx.issues.push({
        code: "custom",
        input: value,
        message:
          "LOG_LEVEL is set to debug/trace in production. Consider setting it to info or warn for production.",
      });
    }
  });

export type AppConfig = z.infer<typeof configSchema>;

let appConfig: AppConfig;

try {
  appConfig = configSchema.parse({
    env: process.env.NODE_ENV,
    port: process.env.PORT,
    logLevel: process.env.LOG_LEVEL,
    jwtSecret: process.env.JWT_SECRET,
    staticPath: process.env.STATIC_PATH,
    dbUrl: process.env.DATABASE_URL,
    adjustImage: {
      defaultQuality: process.env.ADJUST_IMAGE_DEFAULT_QUALITY,
      maxFileSize: process.env.ADJUST_IMAGE_MAX_FILE_SIZE,
      maxFiles: process.env.ADJUST_IMAGE_MAX_FILE_SIZE,
    },
  });
} catch (error) {
  if (error instanceof z.ZodError) {
    console.error("❌ Invalid environment variables:");

    // In ra lỗi chi tiết từ Zod
    error.issues.forEach((err) => {
      console.error(`  - ${err.path.join(".")} : ${err.message}`);
    });

    process.exit(1);
  }

  console.error("An unexpected error occurred during config parsing:", error);
  process.exit(1);
}

const redisConnection = new Redis({
  host: process.env.REDIS_HOST || "localhost",
  port: Number.parseInt(process.env.REDIS_PORT || "6379", 10),
  maxRetriesPerRequest: null,
});

redisConnection.on("connect", () => console.log("Connected to Redis!"));

redisConnection.on("error", (err) =>
  console.error("Redis connection error:", err),
);

export { appConfig, redisConnection };
