# Image Compression Node.js

## Development

### Local Development
```bash
npm install
npm run dev
```

### Docker Development with Hot Reload
```bash
# Using Docker Compose
docker-compose -f docker-compose.dev.yaml up --build

# Using Podman Compose
podman-compose -f docker-compose.dev.yaml up --build

# Or use the convenience script
./dev.sh
```

## Production

### Docker Production
```bash
# Using Docker Compose
docker-compose up --build

# Using Podman Compose
podman-compose up --build

# Or use the convenience script
./prod.sh
```

## Access the Application
```
open http://localhost:3000
```

## Features
- Hot reload in development mode
- Multi-stage Docker builds for optimized production images
- Support for both Docker and Podman
- PostgreSQL and Redis integration
