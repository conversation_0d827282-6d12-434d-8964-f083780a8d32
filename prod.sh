#!/bin/bash

# Ensure script stops on first error
set -e

# Check if podman-compose is installed
if ! command -v podman-compose &> /dev/null; then
    echo "podman-compose is not installed. Please install it first."
    exit 1
fi

# Start production environment
echo "Starting production environment..."
podman-compose -f docker-compose.yaml up --build

# This script will be interrupted by Ctrl+C, which will trigger podman-compose down
# The following will only execute if podman-compose up exits normally
echo "Stopping production environment..."
podman-compose -f docker-compose.yaml down
