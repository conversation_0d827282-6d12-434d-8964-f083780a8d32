{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noForEach": "off"}, "style": {"noNonNullAssertion": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}