FROM node:20-alpine

# Accept build arguments for user/group IDs
ARG USER_ID=1000
ARG GROUP_ID=1000

WORKDIR /app

# Create user with the same ID as host user
RUN addgroup -g ${GROUP_ID} appgroup && \
    adduser -u ${USER_ID} -G appgroup -s /bin/sh -D appuser

# Install pnpm globally
RUN npm install -g pnpm@latest

# Change ownership of the app directory
RUN chown -R appuser:appgroup /app

# Switch to the created user
USER appuser

# Copy package files with correct ownership
COPY --chown=appuser:appgroup package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

EXPOSE 3000

# Start development server with hot reload
CMD ["pnpm", "dev"]
